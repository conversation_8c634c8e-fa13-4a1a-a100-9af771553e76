---
# Process single IP - handles file replacement and command execution
# This file is included for each IP. It expects 'current_ip', 'service_files', 'service_command', and 'service_name' from parent.

- name: "Check server connectivity for {{ current_ip }}"
  ansible.builtin.ping:
  delegate_to: "{{ current_ip }}"
  register: ping_result
  failed_when: false

- name: "Skip processing if server unreachable for {{ current_ip }}"
  ansible.builtin.debug:
    msg: "Warning: Server {{ current_ip }} is unreachable. Skipping file processing for this IP."
  when: ping_result.failed | default(false)

- name: "Check sudo permissions for {{ current_ip }}"
  ansible.builtin.shell: "sudo -n true"
  register: sudo_check_result
  failed_when: false
  changed_when: false
  delegate_to: "{{ current_ip }}"
  when:
    - not (ping_result.failed | default(false))
    - service_sudo | default(false)

- name: "Process files for reachable server {{ current_ip }}"
  when: not (ping_result.failed | default(false))
  block:
    - name: "Display file processing info for {{ current_ip }}"
      ansible.builtin.debug:
        msg: |
          Processing IP: {{ current_ip }} ({{ service_name }})
          ============================================
          Files to process: {{ service_files | join(', ') }}
          String replacement: {{ mongo_old_string }} → {{ mongo_new_string }}

    # Process each file on the current IP
    - name: "Process string replacement in each file for {{ current_ip }}"
      ansible.builtin.include_tasks: task-process-single-file.yml
      loop: "{{ service_files }}"
      loop_control:
        loop_var: current_file
        label: "{{ current_ip }}:{{ current_file }}"

    # Execute post-replacement command if specified
    - name: "Execute post-replacement command for {{ current_ip }}"
      block:
        # Try with sudo first if sudo is required and available
        - name: "Execute command with sudo for {{ current_ip }}"
          ansible.builtin.shell: "sudo {{ service_command }}" # noqa: command-instead-of-shell
          register: command_result_sudo
          failed_when: false
          changed_when: command_result_sudo.rc == 0
          delegate_to: "{{ current_ip }}"
          when:
            - service_sudo | default(false)
            - sudo_check_result is defined
            - sudo_check_result.rc == 0

        # Try without sudo if sudo failed or not available
        - name: "Execute command without sudo for {{ current_ip }}"
          ansible.builtin.shell: "{{ service_command }}" # noqa: command-instead-of-shell
          register: command_result_nosudo
          failed_when: false
          changed_when: command_result_nosudo.rc == 0
          delegate_to: "{{ current_ip }}"
          when: >
            (not (service_sudo | default(false))) or
            (service_sudo | default(false) and
             (sudo_check_result is not defined or sudo_check_result.rc != 0 or
              (command_result_sudo is defined and command_result_sudo.rc != 0)))

        # Set final command result
        - name: "Set final command result for {{ current_ip }}"
          ansible.builtin.set_fact:
            command_result: "{{ command_result_sudo if (command_result_sudo is defined and command_result_sudo.rc == 0) else command_result_nosudo }}"

      rescue:
        - name: "Handle command execution failure for {{ current_ip }}"
          ansible.builtin.debug:
            msg: |
              Command execution failed for {{ current_ip }}, but continuing with next service.
              This error will not stop the playbook execution.

        - name: "Set failed command result for {{ current_ip }}"
          ansible.builtin.set_fact:
            command_result:
              rc: 1
              failed: true
              stderr: "Command execution failed"

      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""

    - name: "Display command execution result for {{ current_ip }}"
      ansible.builtin.debug:
        msg: |
          Command Execution Result for {{ current_ip }}:
          =============================================
          Command: {{ service_command }}
          Exit Code: {{ command_result.rc | default('N/A') }}
          {% if command_result.rc is defined %}
          Status: {{ 'SUCCESS' if command_result.rc == 0 else 'FAILED' }}
          {% if command_result.stdout %}
          Output: {{ command_result.stdout }}
          {% endif %}
          {% if command_result.stderr %}
          Error: {{ command_result.stderr }}
          {% endif %}
          {% else %}
          Status: No command to execute
          {% endif %}
      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""

    - name: "Warning about command failure for {{ current_ip }}"
      ansible.builtin.debug:
        msg: |
          WARNING: Command execution failed on {{ current_ip }}
          This will not stop processing of other servers.
          Command: {{ service_command }}
          Exit Code: {{ command_result.rc }}
          Error: {{ command_result.stderr | default('No error output') }}
      when:
        - service_command is defined
        - service_command != ""
        - service_command | trim != ""
        - command_result.rc is defined
        - command_result.rc != 0
