---
# Process single service - handles multiple IPs and files
# This file is included for each service. It expects 'current_service' from the parent loop.

- name: "Service: Display service processing info: {{ current_service.service_name }}"
  ansible.builtin.debug:
    msg: |
      Processing Service: {{ current_service.service_name }}
      =================================================
      IPs to process: {{ current_service.ips | join(', ') }}
      Files per IP: {{ current_service.files | join(', ') }}
      Command to execute: {{ current_service.command | default('None') }}
      Notes: {{ current_service.notes | default('None') }}

# Process each IP for the current service
- name: "Service: Process each IP: {{ current_service.service_name }}"
  ansible.builtin.include_tasks: task-process-single-ip.yml
  loop: "{{ current_service.ips }}"
  loop_control:
    loop_var: current_ip
    label: "{{ current_service.service_name }} - {{ current_ip }}"
  vars:
    service_files: "{{ current_service.files }}"
    service_command: "{{ current_service.command | default('') }}"
    service_name: "{{ current_service.service_name }}"
